-- SEOFlow 博文管理系统数据库架构
-- 创建时间: 2025-08-05

-- 1. 项目表 (projects)
CREATE TABLE projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    database_url TEXT, -- 目标项目的数据库连接
    api_key TEXT, -- 目标项目的API密钥
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 作者信息表 (authors)
CREATE TABLE authors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    bio TEXT,
    avatar_url TEXT,
    email VARCHAR(255),
    website TEXT,
    social_links JSONB, -- 存储社交媒体链接 {"twitter": "...", "linkedin": "..."}
    expertise_tags TEXT[], -- 专业领域标签
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Prompt模板表 (prompts)
CREATE TABLE prompts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT NOT NULL, -- prompt内容
    variables JSONB, -- 变量定义 {"keyword": "string", "language": "string"}
    category VARCHAR(100), -- prompt分类
    language VARCHAR(10) DEFAULT 'zh-CN',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 博文系列表 (series)
CREATE TABLE series (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    summary TEXT, -- 系列总结，用于生成相关博文
    tags TEXT[],
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 博文表 (posts)
CREATE TABLE posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    series_id UUID REFERENCES series(id) ON DELETE SET NULL,
    author_id UUID REFERENCES authors(id) ON DELETE SET NULL,
    prompt_id UUID REFERENCES prompts(id) ON DELETE SET NULL,

    -- 基本信息
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,

    -- SEO信息
    meta_title VARCHAR(255),
    meta_description VARCHAR(500),
    keywords TEXT[],
    canonical_url TEXT,

    -- 分类和标签
    category VARCHAR(100),
    tags TEXT[],

    -- 语言和本地化
    language VARCHAR(10) DEFAULT 'zh-CN',

    -- 生成信息
    generation_params JSONB, -- 生成参数 {"keywords": "...", "style": "..."}
    ai_summary TEXT, -- AI生成的文章总结，用于系列关联

    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    published_at TIMESTAMP WITH TIME ZONE,

    -- 统计信息
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 系列博文关联表 (series_posts) - 用于维护系列内博文的顺序
CREATE TABLE series_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    series_id UUID REFERENCES series(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(series_id, post_id),
    UNIQUE(series_id, order_index)
);

-- 7. 博文生成历史表 (generation_history)
CREATE TABLE generation_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    prompt_id UUID REFERENCES prompts(id) ON DELETE SET NULL,

    input_params JSONB NOT NULL, -- 输入参数
    generated_content TEXT NOT NULL, -- 生成的内容
    generation_time INTEGER, -- 生成耗时(秒)
    tokens_used INTEGER, -- 使用的token数量
    cost DECIMAL(10,4), -- 生成成本

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_posts_project_id ON posts(project_id);
CREATE INDEX idx_posts_series_id ON posts(series_id);
CREATE INDEX idx_posts_author_id ON posts(author_id);
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_language ON posts(language);
CREATE INDEX idx_posts_published_at ON posts(published_at);
CREATE INDEX idx_posts_keywords ON posts USING GIN(keywords);
CREATE INDEX idx_posts_tags ON posts USING GIN(tags);

CREATE INDEX idx_authors_project_id ON authors(project_id);
CREATE INDEX idx_prompts_project_id ON prompts(project_id);
CREATE INDEX idx_series_project_id ON series(project_id);
CREATE INDEX idx_generation_history_project_id ON generation_history(project_id);
CREATE INDEX idx_generation_history_post_id ON generation_history(post_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_authors_updated_at BEFORE UPDATE ON authors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON prompts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_series_updated_at BEFORE UPDATE ON series FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认项目
INSERT INTO projects (name, description) VALUES
('默认项目', '系统默认项目，用于存储本地生成的博文和配置');
