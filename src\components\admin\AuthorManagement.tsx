import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

export default function AuthorManagement() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">作者管理</h1>
        <p className="text-gray-600">管理博文作者信息和资料</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>功能开发中</CardTitle>
          <CardDescription>作者管理功能正在开发中，敬请期待</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">
            此页面将包含：作者列表、个人资料管理、专业标签、社交链接等功能
          </p>
        </CardContent>
      </Card>
    </div>
  );
}