import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Search,
  PenTool,
  FileText,
  MessageSquare,
  Users,
  Settings,
  BarChart3,
  Menu,
  X,
  Home,
  ChevronDown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatabaseService, setCurrentProject } from '@/lib/supabase';
import { Project } from '@/types/database';

const navigation = [
  { name: '仪表板', href: '/admin/dashboard', icon: BarChart3 },
  { name: '生成博文', href: '/admin/generate', icon: PenTool },
  { name: '博文管理', href: '/admin/posts', icon: FileText },
  { name: 'Prompt管理', href: '/admin/prompts', icon: MessageSquare },
  { name: '作者管理', href: '/admin/authors', icon: Users },
  { name: '项目管理', href: '/admin/projects', icon: Settings },
];

export default function AdminLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [currentProject, setCurrentProjectState] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      const data = await DatabaseService.getProjects();
      setProjects(data);

      // 设置默认项目
      if (data.length > 0 && !currentProject) {
        const defaultProject = data.find(p => p.name === '默认项目') || data[0];
        setCurrentProjectState(defaultProject.id);
        setCurrentProject(defaultProject.id);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectChange = (projectId: string) => {
    setCurrentProjectState(projectId);
    setCurrentProject(projectId);
  };

  const currentProjectData = projects.find(p => p.id === currentProject);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 移动端侧边栏背景 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Search className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold">SEOFlow</span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* 项目选择器 */}
          <div className="p-4 border-b">
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              当前项目
            </label>
            <Select value={currentProject} onValueChange={handleProjectChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择项目" />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    <div className="flex items-center gap-2">
                      <span>{project.name}</span>
                      {project.status === 'inactive' && (
                        <Badge variant="secondary" className="text-xs">
                          已停用
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 px-4 py-4 space-y-1">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`
                    flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors
                    ${isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  <item.icon className="w-5 h-5" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* 底部信息 */}
          <div className="p-4 border-t">
            <Link
              to="/"
              className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900"
            >
              <Home className="w-4 h-4" />
              返回首页
            </Link>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="lg:pl-64">
        {/* 顶部导航栏 */}
        <div className="sticky top-0 z-40 bg-white border-b px-4 py-4 lg:px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="w-5 h-5" />
              </Button>

              {currentProjectData && (
                <div className="hidden sm:block">
                  <h1 className="text-lg font-semibold text-gray-900">
                    {currentProjectData.name}
                  </h1>
                  {currentProjectData.description && (
                    <p className="text-sm text-gray-600">
                      {currentProjectData.description}
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-4">
              <Badge variant="outline" className="hidden sm:inline-flex">
                管理模式
              </Badge>
            </div>
          </div>
        </div>

        {/* 页面内容 */}
        <main className="p-4 lg:p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            </div>
          ) : (
            <Outlet />
          )}
        </main>
      </div>
    </div>
  );
}