import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

// Supabase配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

// 创建Supabase客户端
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// 当前项目ID（可以通过上下文或状态管理来动态设置）
let currentProjectId: string | null = null;

export const setCurrentProject = (projectId: string) => {
  currentProjectId = projectId;
};

export const getCurrentProject = () => currentProjectId;

// 数据库操作辅助函数
export class DatabaseService {
  // 项目相关操作
  static async getProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  static async createProject(project: Database['public']['Tables']['projects']['Insert']) {
    const { data, error } = await supabase
      .from('projects')
      .insert(project)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateProject(id: string, updates: Database['public']['Tables']['projects']['Update']) {
    const { data, error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteProject(id: string) {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // 作者相关操作
  static async getAuthors(projectId?: string) {
    const pid = projectId || currentProjectId;
    if (!pid) throw new Error('No project selected');

    const { data, error } = await supabase
      .from('authors')
      .select('*')
      .eq('project_id', pid)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  static async createAuthor(author: Database['public']['Tables']['authors']['Insert']) {
    const { data, error } = await supabase
      .from('authors')
      .insert({
        ...author,
        project_id: author.project_id || currentProjectId!
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateAuthor(id: string, updates: Database['public']['Tables']['authors']['Update']) {
    const { data, error } = await supabase
      .from('authors')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteAuthor(id: string) {
    const { error } = await supabase
      .from('authors')
      .update({ status: 'inactive' })
      .eq('id', id);

    if (error) throw error;
  }

  // Prompt相关操作
  static async getPrompts(projectId?: string) {
    const pid = projectId || currentProjectId;
    if (!pid) throw new Error('No project selected');

    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('project_id', pid)
      .in('status', ['active', 'draft'])
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  static async createPrompt(prompt: Database['public']['Tables']['prompts']['Insert']) {
    const { data, error } = await supabase
      .from('prompts')
      .insert({
        ...prompt,
        project_id: prompt.project_id || currentProjectId!
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updatePrompt(id: string, updates: Database['public']['Tables']['prompts']['Update']) {
    const { data, error } = await supabase
      .from('prompts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deletePrompt(id: string) {
    const { error } = await supabase
      .from('prompts')
      .update({ status: 'inactive' })
      .eq('id', id);

    if (error) throw error;
  }

  // 系列相关操作
  static async getSeries(projectId?: string) {
    const pid = projectId || currentProjectId;
    if (!pid) throw new Error('No project selected');

    const { data, error } = await supabase
      .from('series')
      .select('*')
      .eq('project_id', pid)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  static async createSeries(series: Database['public']['Tables']['series']['Insert']) {
    const { data, error } = await supabase
      .from('series')
      .insert({
        ...series,
        project_id: series.project_id || currentProjectId!
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateSeries(id: string, updates: Database['public']['Tables']['series']['Update']) {
    const { data, error } = await supabase
      .from('series')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteSeries(id: string) {
    const { error } = await supabase
      .from('series')
      .update({ status: 'inactive' })
      .eq('id', id);

    if (error) throw error;
  }

  // 博文相关操作
  static async getPosts(projectId?: string, filters?: {
    status?: string;
    series_id?: string;
    author_id?: string;
    language?: string;
  }) {
    const pid = projectId || currentProjectId;
    if (!pid) throw new Error('No project selected');

    let query = supabase
      .from('posts')
      .select(`
        *,
        author:authors(*),
        series:series(*),
        prompt:prompts(*)
      `)
      .eq('project_id', pid);

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.series_id) {
      query = query.eq('series_id', filters.series_id);
    }
    if (filters?.author_id) {
      query = query.eq('author_id', filters.author_id);
    }
    if (filters?.language) {
      query = query.eq('language', filters.language);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) throw error;
    return data;
  }

  static async getPost(id: string) {
    const { data, error } = await supabase
      .from('posts')
      .select(`
        *,
        author:authors(*),
        series:series(*),
        prompt:prompts(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  static async createPost(post: Database['public']['Tables']['posts']['Insert']) {
    const { data, error } = await supabase
      .from('posts')
      .insert({
        ...post,
        project_id: post.project_id || currentProjectId!
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updatePost(id: string, updates: Database['public']['Tables']['posts']['Update']) {
    const { data, error } = await supabase
      .from('posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deletePost(id: string) {
    const { error } = await supabase
      .from('posts')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async publishPost(id: string) {
    const { data, error } = await supabase
      .from('posts')
      .update({
        status: 'published',
        published_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // 生成历史相关操作
  static async createGenerationHistory(history: Database['public']['Tables']['generation_history']['Insert']) {
    const { data, error } = await supabase
      .from('generation_history')
      .insert({
        ...history,
        project_id: history.project_id || currentProjectId!
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async getGenerationHistory(postId: string) {
    const { data, error } = await supabase
      .from('generation_history')
      .select('*')
      .eq('post_id', postId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  // 统计相关操作
  static async getProjectStats(projectId?: string) {
    const pid = projectId || currentProjectId;
    if (!pid) throw new Error('No project selected');

    const [postsResult, authorsResult, seriesResult, promptsResult] = await Promise.all([
      supabase.from('posts').select('status').eq('project_id', pid),
      supabase.from('authors').select('id').eq('project_id', pid).eq('status', 'active'),
      supabase.from('series').select('id').eq('project_id', pid).eq('status', 'active'),
      supabase.from('prompts').select('id').eq('project_id', pid).in('status', ['active', 'draft'])
    ]);

    const posts = postsResult.data || [];
    const publishedPosts = posts.filter(p => p.status === 'published').length;
    const draftPosts = posts.filter(p => p.status === 'draft').length;

    return {
      totalPosts: posts.length,
      publishedPosts,
      draftPosts,
      totalAuthors: <AUTHORS>
      totalSeries: seriesResult.data?.length || 0,
      totalPrompts: promptsResult.data?.length || 0
    };
  }
}