// 数据库类型定义
export interface Database {
  public: {
    Tables: {
      projects: {
        Row: Project;
        Insert: Omit<Project, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at'>>;
      };
      authors: {
        Row: Author;
        Insert: Omit<Author, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Author, 'id' | 'created_at' | 'updated_at'>>;
      };
      prompts: {
        Row: Prompt;
        Insert: Omit<Prompt, 'id' | 'created_at' | 'updated_at' | 'usage_count'>;
        Update: Partial<Omit<Prompt, 'id' | 'created_at' | 'updated_at'>>;
      };
      series: {
        Row: Series;
        Insert: Omit<Series, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Series, 'id' | 'created_at' | 'updated_at'>>;
      };
      posts: {
        Row: Post;
        Insert: Omit<Post, 'id' | 'created_at' | 'updated_at' | 'view_count' | 'like_count'>;
        Update: Partial<Omit<Post, 'id' | 'created_at' | 'updated_at'>>;
      };
      series_posts: {
        Row: SeriesPost;
        Insert: Omit<SeriesPost, 'id' | 'created_at'>;
        Update: Partial<Omit<SeriesPost, 'id' | 'created_at'>>;
      };
      generation_history: {
        Row: GenerationHistory;
        Insert: Omit<GenerationHistory, 'id' | 'created_at'>;
        Update: Partial<Omit<GenerationHistory, 'id' | 'created_at'>>;
      };
    };
  };
}

// 基础类型定义
export interface Project {
  id: string;
  name: string;
  description?: string;
  database_url?: string;
  api_key?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Author {
  id: string;
  project_id: string;
  name: string;
  bio?: string;
  avatar_url?: string;
  email?: string;
  website?: string;
  social_links?: Record<string, string>;
  expertise_tags?: string[];
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Prompt {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  content: string;
  variables?: Record<string, string>;
  category?: string;
  language: string;
  status: 'active' | 'inactive' | 'draft';
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface Series {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  summary?: string;
  tags?: string[];
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Post {
  id: string;
  project_id: string;
  series_id?: string;
  author_id?: string;
  prompt_id?: string;
  title: string;
  slug?: string;
  content: string;
  excerpt?: string;
  meta_title?: string;
  meta_description?: string;
  keywords?: string[];
  canonical_url?: string;
  category?: string;
  tags?: string[];
  language: string;
  generation_params?: Record<string, any>;
  ai_summary?: string;
  status: 'draft' | 'published' | 'archived';
  published_at?: string;
  view_count: number;
  like_count: number;
  created_at: string;
  updated_at: string;
}

export interface SeriesPost {
  id: string;
  series_id: string;
  post_id: string;
  order_index: number;
  created_at: string;
}

export interface GenerationHistory {
  id: string;
  project_id: string;
  post_id: string;
  prompt_id?: string;
  input_params: Record<string, any>;
  generated_content: string;
  generation_time?: number;
  tokens_used?: number;
  cost?: number;
  created_at: string;
}

// 扩展类型，包含关联数据
export interface PostWithRelations extends Post {
  author?: Author;
  series?: Series;
  prompt?: Prompt;
}

export interface SeriesWithPosts extends Series {
  posts?: Post[];
  post_count?: number;
}

export interface AuthorWithStats extends Author {
  post_count?: number;
  total_views?: number;
}

// 表单类型
export interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  meta_title?: string;
  meta_description?: string;
  keywords?: string[];
  category?: string;
  tags?: string[];
  language: string;
  series_id?: string;
  author_id?: string;
  status: 'draft' | 'published';
}

export interface AuthorFormData {
  name: string;
  bio?: string;
  email?: string;
  website?: string;
  expertise_tags?: string[];
}

export interface PromptFormData {
  name: string;
  description?: string;
  content: string;
  category?: string;
  language: string;
  variables?: Record<string, string>;
}

export interface SeriesFormData {
  name: string;
  description?: string;
  tags?: string[];
}

export interface ProjectFormData {
  name: string;
  description?: string;
  database_url?: string;
  api_key?: string;
}

// 生成博文的参数类型
export interface GeneratePostParams {
  keywords?: string;
  title?: string;
  language: string;
  series_id?: string;
  author_id?: string;
  prompt_id?: string;
  style?: string;
  length?: 'short' | 'medium' | 'long';
  tone?: 'professional' | 'casual' | 'technical' | 'friendly';
}