# SEOFlow - AI博文管理系统

一个基于React + TypeScript + Supabase的AI驱动博文生成和管理系统。

## 功能特性

### 🤖 博文生成子版块
- ✅ 支持输入关键词、文章标题来生成博文
- ✅ 可设置博文语言（支持中文、英文、日文、韩文等多种语言）
- ✅ 可手动设置或自动根据博文内容生成博文标题、SEO信息、博文类型或标签
- ✅ 可设置博文专题系列，系列内博文具有上下关联性
- ✅ 可设置作者信息或选择现有作者信息
- ✅ 可设置prompt或选择现有prompt模板

### 📝 博文管理子版块
- 🚧 可预览、编辑、发布、删除博文（开发中）

### 💬 Prompt管理子版块
- 🚧 可以预览、编辑、发布、删除prompt（开发中）

### 👥 作者信息管理子版块
- 🚧 可以预览、编辑、发布、删除作者信息（开发中）

### 🗄️ 数据库存储
- ✅ 完整的Supabase数据库架构设计
- ✅ 支持项目、作者、博文、系列、Prompt等数据管理
- ✅ 包含生成历史记录和统计功能

### 🔄 项目管理子版块
- 🚧 项目管理，可以连接切换不同项目的数据库（开发中）
- 🚧 将本项目数据库中生成的博文和作者信息，推送至指定项目的数据库（开发中）

## 技术栈

- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite 7
- **UI组件库**: Radix UI + Tailwind CSS
- **路由**: React Router DOM
- **数据库**: Supabase (PostgreSQL)
- **表单处理**: React Hook Form + Zod
- **动画**: Framer Motion
- **图标**: Lucide React

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd seofollow
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
复制 `.env.example` 到 `.env` 并填入你的配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# Supabase配置
VITE_SUPABASE_URL=your-supabase-project-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# AI服务配置（可选，用于博文生成）
VITE_OPENAI_API_KEY=your-openai-api-key
VITE_ANTHROPIC_API_KEY=your-anthropic-api-key
```

### 4. 设置数据库
在你的Supabase项目中执行 `database-schema.sql` 文件中的SQL语句来创建数据库表。

### 5. 启动开发服务器
```bash
npm run dev
```

访问 `http://localhost:5173` 查看应用。

## 项目结构

```
src/
├── components/
│   ├── admin/              # 管理系统页面组件
│   │   ├── AdminLayout.tsx # 管理系统布局
│   │   ├── Dashboard.tsx   # 仪表板
│   │   ├── PostGenerate.tsx # 博文生成
│   │   └── ...
│   ├── ui/                 # 基础UI组件
│   └── SEOFlowLandingPage.tsx # 落地页
├── lib/
│   ├── supabase.ts        # Supabase客户端和数据库操作
│   └── utils.ts           # 工具函数
├── types/
│   └── database.ts        # 数据库类型定义
└── App.tsx                # 主应用组件
```

## 使用指南

### 访问管理系统
1. 访问首页后，点击导航栏中的"管理系统"或直接访问 `/admin`
2. 系统会自动加载默认项目，你也可以在侧边栏切换项目

### 生成博文
1. 在管理系统中点击"生成博文"
2. 输入关键词或文章标题
3. 选择语言、长度、语调等参数
4. 可选择作者、系列、Prompt模板
5. 点击"生成博文"按钮
6. 预览生成结果并保存为草稿

### 数据库架构
系统包含以下主要数据表：
- `projects`: 项目管理
- `authors`: 作者信息
- `prompts`: Prompt模板
- `series`: 博文系列
- `posts`: 博文内容
- `generation_history`: 生成历史

## 开发计划

- [ ] 完善博文管理功能（编辑、发布、删除）
- [ ] 实现Prompt管理CRUD操作
- [ ] 实现作者信息管理CRUD操作
- [ ] 添加项目间数据推送功能
- [ ] 集成真实的AI服务（OpenAI、Anthropic等）
- [ ] 添加博文SEO分析功能
- [ ] 实现批量操作功能
- [ ] 添加数据导入导出功能

## 贡献

欢迎提交Issue和Pull Request来帮助改进这个项目。

## 许可证

MIT License
