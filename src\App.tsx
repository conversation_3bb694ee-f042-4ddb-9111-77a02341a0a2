import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import SEOFlowLandingPage from './components/SEOFlowLandingPage';
import AdminLayout from './components/admin/AdminLayout';
import Dashboard from './components/admin/Dashboard';
import PostGenerate from './components/admin/PostGenerate';
import PostManagement from './components/admin/PostManagement';
import PromptManagement from './components/admin/PromptManagement';
import AuthorManagement from './components/admin/AuthorManagement';
import ProjectManagement from './components/admin/ProjectManagement';

function App() {
  return (
    <Router>
      <Routes>
        {/* 落地页 */}
        <Route path="/" element={<SEOFlowLandingPage />} />

        {/* 管理系统 */}
        <Route path="/admin" element={<AdminLayout />}>
          <Route index element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="generate" element={<PostGenerate />} />
          <Route path="posts" element={<PostManagement />} />
          <Route path="prompts" element={<PromptManagement />} />
          <Route path="authors" element={<AuthorManagement />} />
          <Route path="projects" element={<ProjectManagement />} />
        </Route>

        {/* 默认重定向到落地页 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

export default App;