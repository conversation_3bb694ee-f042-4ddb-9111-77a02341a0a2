import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Wand2, Save, Eye, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { DatabaseService } from '../../lib/supabase';
import { Author, Prompt, Series, GeneratePostParams } from '../../types/database';

const languages = [
  { value: 'zh-CN', label: '中文' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
];

const tones = [
  { value: 'professional', label: '专业' },
  { value: 'casual', label: '轻松' },
  { value: 'technical', label: '技术' },
  { value: 'friendly', label: '友好' },
];

const lengths = [
  { value: 'short', label: '短文 (500-800字)' },
  { value: 'medium', label: '中等 (800-1500字)' },
  { value: 'long', label: '长文 (1500-3000字)' },
];

export default function PostGenerate() {
  const [formData, setFormData] = useState<GeneratePostParams>({
    keywords: '',
    title: '',
    language: 'zh-CN',
    length: 'medium',
    tone: 'professional',
  });

  const [authors, setAuthors] = useState<Author[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [series, setSeries] = useState<Series[]>([]);
  const [generatedContent, setGeneratedContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [loading, setLoading] = useState(true);

  const navigate = useNavigate();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [authorsData, promptsData, seriesData] = await Promise.all([
        DatabaseService.getAuthors(),
        DatabaseService.getPrompts(),
        DatabaseService.getSeries(),
      ]);

      setAuthors(authorsData);
      setPrompts(promptsData);
      setSeries(seriesData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof GeneratePostParams, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerate = async () => {
    if (!formData.keywords && !formData.title) {
      alert('请输入关键词或文章标题');
      return;
    }

    setIsGenerating(true);

    try {
      // 这里应该调用AI服务生成内容
      // 目前使用模拟内容
      await new Promise(resolve => setTimeout(resolve, 3000));

      const mockContent = `# ${formData.title || '基于关键词生成的文章标题'}

## 引言

这是一篇基于关键词"${formData.keywords}"生成的${lengths.find(l => l.value === formData.length)?.label}文章。

## 主要内容

根据您选择的${tones.find(t => t.value === formData.tone)?.label}语调，我们为您生成了以下内容：

### 第一部分

这里是文章的第一部分内容，详细阐述了关键词相关的概念和重要性。

### 第二部分

这里是文章的第二部分内容，提供了实用的建议和最佳实践。

### 第三部分

这里是文章的第三部分内容，总结了关键要点并提供了行动建议。

## 结论

通过本文的介绍，读者应该能够全面了解相关主题，并能够在实际工作中应用这些知识。

---

*本文由 SEOFlow AI 自动生成，语言：${languages.find(l => l.value === formData.language)?.label}*`;

      setGeneratedContent(mockContent);
    } catch (error) {
      console.error('Failed to generate content:', error);
      alert('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = async () => {
    if (!generatedContent) {
      alert('请先生成内容');
      return;
    }

    try {
      const postData = {
        title: formData.title || '基于关键词生成的文章标题',
        content: generatedContent,
        language: formData.language,
        status: 'draft' as const,
        author_id: formData.author_id,
        series_id: formData.series_id,
        prompt_id: formData.prompt_id,
        generation_params: formData,
        keywords: formData.keywords ? [formData.keywords] : undefined,
      };

      await DatabaseService.createPost(postData);
      alert('博文已保存为草稿');
      navigate('/admin/posts');
    } catch (error) {
      console.error('Failed to save post:', error);
      alert('保存失败，请重试');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">加载数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">生成博文</h1>
        <p className="text-gray-600">使用AI自动生成高质量的SEO博文</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 生成配置 */}
        <Card>
          <CardHeader>
            <CardTitle>生成配置</CardTitle>
            <CardDescription>设置博文生成参数</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                关键词 *
              </label>
              <Input
                placeholder="输入主要关键词，如：SEO优化、内容营销"
                value={formData.keywords}
                onChange={(e) => handleInputChange('keywords', e.target.value)}
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                文章标题（可选）
              </label>
              <Input
                placeholder="留空将自动生成标题"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  语言
                </label>
                <Select
                  value={formData.language}
                  onValueChange={(value) => handleInputChange('language', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  文章长度
                </label>
                <Select
                  value={formData.length}
                  onValueChange={(value) => handleInputChange('length', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {lengths.map((length) => (
                      <SelectItem key={length.value} value={length.value}>
                        {length.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                语调风格
              </label>
              <Select
                value={formData.tone}
                onValueChange={(value) => handleInputChange('tone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {tones.map((tone) => (
                    <SelectItem key={tone.value} value={tone.value}>
                      {tone.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                选择作者（可选）
              </label>
              <Select
                value={formData.author_id || ''}
                onValueChange={(value) => handleInputChange('author_id', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择作者" />
                </SelectTrigger>
                <SelectContent>
                  {authors.map((author) => (
                    <SelectItem key={author.id} value={author.id}>
                      {author.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                选择系列（可选）
              </label>
              <Select
                value={formData.series_id || ''}
                onValueChange={(value) => handleInputChange('series_id', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择系列" />
                </SelectTrigger>
                <SelectContent>
                  {series.map((s) => (
                    <SelectItem key={s.id} value={s.id}>
                      {s.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                选择Prompt模板（可选）
              </label>
              <Select
                value={formData.prompt_id || ''}
                onValueChange={(value) => handleInputChange('prompt_id', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择模板" />
                </SelectTrigger>
                <SelectContent>
                  {prompts.map((prompt) => (
                    <SelectItem key={prompt.id} value={prompt.id}>
                      {prompt.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={handleGenerate}
              disabled={isGenerating}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  生成中...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  生成博文
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 生成结果 */}
        <Card>
          <CardHeader>
            <CardTitle>生成结果</CardTitle>
            <CardDescription>
              {generatedContent ? '博文已生成，您可以预览或保存' : '点击生成按钮开始创建博文'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {generatedContent ? (
              <div className="space-y-4">
                <Tabs defaultValue="preview">
                  <TabsList>
                    <TabsTrigger value="preview">预览</TabsTrigger>
                    <TabsTrigger value="markdown">Markdown</TabsTrigger>
                  </TabsList>

                  <TabsContent value="preview" className="mt-4">
                    <div className="prose prose-sm max-w-none border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
                      <div dangerouslySetInnerHTML={{
                        __html: generatedContent.replace(/\n/g, '<br>').replace(/#{1,6}\s/g, '<strong>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                      }} />
                    </div>
                  </TabsContent>

                  <TabsContent value="markdown" className="mt-4">
                    <Textarea
                      value={generatedContent}
                      onChange={(e) => setGeneratedContent(e.target.value)}
                      className="min-h-96 font-mono text-sm"
                      placeholder="生成的内容将显示在这里..."
                    />
                  </TabsContent>
                </Tabs>

                <div className="flex gap-2">
                  <Button onClick={handleSave} className="flex-1">
                    <Save className="w-4 h-4 mr-2" />
                    保存为草稿
                  </Button>
                  <Button variant="outline" onClick={() => setGeneratedContent('')}>
                    重新生成
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Wand2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>请配置生成参数并点击生成按钮</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}