"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Search, 
  BarChart3, 
  Zap, 
  Globe, 
  Users, 
  CheckCircle, 
  Star, 
  ArrowRight, 
  Mail, 
  Phone, 
  MapPin,
  Menu,
  X,
  ChevronDown,
  TrendingUp,
  Target,
  Clock,
  DollarSign
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Carousel, CarouselContent, CarouselItem, type CarouselApi } from "@/components/ui/carousel";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface Plan {
  name: string;
  info: string;
  price: {
    monthly: number;
    yearly: number;
  };
  features: {
    text: string;
    tooltip?: string;
  }[];
  btn: {
    text: string;
    href: string;
  };
  highlighted?: boolean;
}

interface Testimonial {
  quote: string;
  name: string;
  role: string;
  company: string;
  avatar: string;
}

const testimonials: Testimonial[] = [
  {
    quote: "SEOFlow transformed our content strategy. We've seen a 300% increase in organic traffic within 3 months.",
    name: "Alex Chen",
    role: "Indie Developer",
    company: "TechStartup",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
  },
  {
    quote: "As a cross-border seller, SEOFlow's automation saved me 20 hours per week on content creation.",
    name: "Maria Rodriguez",
    role: "E-commerce Manager",
    company: "GlobalShop",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
  },
  {
    quote: "The ROI tracking feature helped us optimize our content budget and increase conversions by 150%.",
    name: "David Kim",
    role: "SaaS Founder",
    company: "AppBuilder",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
  },
  {
    quote: "Perfect for small teams. The AI-powered suggestions are incredibly accurate and save tons of time.",
    name: "Sarah Johnson",
    role: "Content Creator",
    company: "BlogMaster",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
  },
  {
    quote: "SEOFlow's automation helped us scale from 10K to 100K monthly visitors without hiring more staff.",
    name: "James Wilson",
    role: "Website Owner",
    company: "TechBlog",
    avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face"
  }
];

const plans: Plan[] = [
  {
    name: "Starter",
    info: "Perfect for indie developers getting started",
    price: {
      monthly: 29,
      yearly: 290
    },
    features: [
      { text: "Up to 10 content pieces/month" },
      { text: "Basic SEO analysis" },
      { text: "Email support" },
      { text: "Content templates" },
      { text: "Basic analytics" }
    ],
    btn: {
      text: "Start Free Trial",
      href: "#"
    }
  },
  {
    name: "Professional",
    info: "For growing SaaS and e-commerce businesses",
    price: {
      monthly: 79,
      yearly: 790
    },
    features: [
      { text: "Up to 50 content pieces/month" },
      { text: "Advanced SEO optimization", tooltip: "Includes keyword research, competitor analysis, and SERP tracking" },
      { text: "Priority support" },
      { text: "Custom templates" },
      { text: "Advanced analytics & ROI tracking" },
      { text: "API access" },
      { text: "Multi-language support" }
    ],
    btn: {
      text: "Get Started",
      href: "#"
    },
    highlighted: true
  },
  {
    name: "Enterprise",
    info: "For large teams and agencies",
    price: {
      monthly: 199,
      yearly: 1990
    },
    features: [
      { text: "Unlimited content pieces" },
      { text: "White-label solution" },
      { text: "Dedicated account manager" },
      { text: "Custom integrations" },
      { text: "Advanced team collaboration" },
      { text: "Custom reporting" },
      { text: "SLA guarantee" }
    ],
    btn: {
      text: "Contact Sales",
      href: "#"
    }
  }
];

function PricingFrequencyToggle({ 
  frequency, 
  setFrequency 
}: { 
  frequency: 'monthly' | 'yearly';
  setFrequency: (freq: 'monthly' | 'yearly') => void;
}) {
  return (
    <div className="bg-muted/30 mx-auto flex w-fit rounded-full border p-1">
      {(['monthly', 'yearly'] as const).map((freq) => (
        <button
          key={freq}
          onClick={() => setFrequency(freq)}
          className="relative px-4 py-1 text-sm capitalize"
        >
          <span className="relative z-10">{freq}</span>
          {frequency === freq && (
            <motion.span
              layoutId="frequency"
              transition={{ type: 'spring', duration: 0.4 }}
              className="bg-foreground absolute inset-0 z-10 rounded-full mix-blend-difference"
            />
          )}
        </button>
      ))}
    </div>
  );
}

function PricingCard({ 
  plan, 
  frequency 
}: { 
  plan: Plan; 
  frequency: 'monthly' | 'yearly';
}) {
  return (
    <Card className={`relative ${plan.highlighted ? 'border-primary shadow-lg' : ''}`}>
      {plan.highlighted && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-primary text-primary-foreground">
            <Star className="w-3 h-3 mr-1" />
            Most Popular
          </Badge>
        </div>
      )}
      
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {plan.name}
          {frequency === 'yearly' && (
            <Badge variant="secondary" className="text-xs">
              Save {Math.round(((plan.price.monthly * 12 - plan.price.yearly) / (plan.price.monthly * 12)) * 100)}%
            </Badge>
          )}
        </CardTitle>
        <CardDescription>{plan.info}</CardDescription>
        <div className="mt-4">
          <span className="text-3xl font-bold">${plan.price[frequency]}</span>
          <span className="text-muted-foreground">
            /{frequency === 'monthly' ? 'month' : 'year'}
          </span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {plan.features.map((feature, index) => (
            <div key={index} className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className={feature.tooltip ? 'cursor-help border-b border-dashed' : ''}>
                      {feature.text}
                    </span>
                  </TooltipTrigger>
                  {feature.tooltip && (
                    <TooltipContent>
                      <p>{feature.tooltip}</p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            </div>
          ))}
        </div>
        
        <Button 
          className="w-full" 
          variant={plan.highlighted ? 'default' : 'outline'}
        >
          {plan.btn.text}
        </Button>
      </CardContent>
    </Card>
  );
}

function TestimonialsSection() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) return;

    const timer = setTimeout(() => {
      if (api.selectedScrollSnap() + 1 === api.scrollSnapList().length) {
        setCurrent(0);
        api.scrollTo(0);
      } else {
        api.scrollNext();
        setCurrent(current + 1);
      }
    }, 4000);

    return () => clearTimeout(timer);
  }, [api, current]);

  return (
    <section className="py-20 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Trusted by 10,000+ Developers & Marketers
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            See how SEOFlow is helping businesses grow their organic traffic and revenue
          </p>
        </div>
        
        <Carousel setApi={setApi} className="w-full max-w-4xl mx-auto">
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="md:basis-1/2">
                <Card className="h-full">
                  <CardContent className="p-6">
                    <div className="flex flex-col h-full">
                      <div className="flex-1">
                        <p className="text-muted-foreground mb-4 italic">
                          "{testimonial.quote}"
                        </p>
                      </div>
                      <div className="flex items-center gap-3 mt-4">
                        <Avatar>
                          <AvatarImage src={testimonial.avatar} />
                          <AvatarFallback>{testimonial.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-semibold">{testimonial.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {testimonial.role} at {testimonial.company}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </section>
  );
}

export default function MarTechLandingPage() {
  const [frequency, setFrequency] = useState<'monthly' | 'yearly'>('monthly');
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Search className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold">SEOFlow</span>
            </div>
            
            <nav className="hidden md:flex items-center gap-6">
              <a href="#features" className="text-muted-foreground hover:text-foreground">Features</a>
              <a href="#pricing" className="text-muted-foreground hover:text-foreground">Pricing</a>
              <a href="#testimonials" className="text-muted-foreground hover:text-foreground">Reviews</a>
              <a href="#faq" className="text-muted-foreground hover:text-foreground">FAQ</a>
            </nav>
            
            <div className="hidden md:flex items-center gap-3">
              <Button variant="ghost">Sign In</Button>
              <Button>Start Free Trial</Button>
            </div>
            
            <Button 
              variant="ghost" 
              size="icon" 
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
          
          {isMenuOpen && (
            <div className="md:hidden mt-4 pb-4 border-t pt-4">
              <nav className="flex flex-col gap-3">
                <a href="#features" className="text-muted-foreground hover:text-foreground">Features</a>
                <a href="#pricing" className="text-muted-foreground hover:text-foreground">Pricing</a>
                <a href="#testimonials" className="text-muted-foreground hover:text-foreground">Reviews</a>
                <a href="#faq" className="text-muted-foreground hover:text-foreground">FAQ</a>
                <div className="flex flex-col gap-2 mt-3">
                  <Button variant="ghost" className="justify-start">Sign In</Button>
                  <Button className="justify-start">Start Free Trial</Button>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <Badge className="mb-6" variant="secondary">
              <Zap className="w-3 h-3 mr-1" />
              AI-Powered SEO Automation
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              Scale Your Content Marketing Without the Headache
            </h1>
            
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Automate SEO content creation, track ROI, and grow organic traffic. 
              Built for indie developers, SaaS founders, and e-commerce professionals who need results, not complexity.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="text-lg px-8">
                Start Free 14-Day Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8">
                Watch Demo
              </Button>
            </div>
            
            <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                No credit card required
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Setup in 5 minutes
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Cancel anytime
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Proof Section */}
      <section className="py-16 bg-muted/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <p className="text-muted-foreground mb-8">Trusted by 10,000+ developers and marketers worldwide</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
              <div className="text-2xl font-bold">Shopify</div>
              <div className="text-2xl font-bold">GitHub</div>
              <div className="text-2xl font-bold">Stripe</div>
              <div className="text-2xl font-bold">Vercel</div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">300%</div>
              <p className="text-muted-foreground">Average traffic increase</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">20hrs</div>
              <p className="text-muted-foreground">Saved per week</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">$50K</div>
              <p className="text-muted-foreground">Revenue generated</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to Dominate SEO
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Stop juggling multiple tools. Get AI-powered content creation, SEO optimization, 
              and performance tracking in one platform.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <BarChart3 className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>AI Content Generation</CardTitle>
                <CardDescription>
                  Generate SEO-optimized blog posts, product descriptions, and landing pages in minutes, not hours.
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <TrendingUp className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Keyword Research & Tracking</CardTitle>
                <CardDescription>
                  Find low-competition keywords and track your rankings across multiple search engines and locations.
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Target className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Competitor Analysis</CardTitle>
                <CardDescription>
                  Spy on your competitors' content strategies and discover their top-performing keywords and pages.
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Clock className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Content Automation</CardTitle>
                <CardDescription>
                  Schedule and auto-publish content across multiple platforms. Set it once, let it run forever.
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <DollarSign className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>ROI Tracking</CardTitle>
                <CardDescription>
                  Track revenue attribution from organic traffic. Know exactly which content drives sales.
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Globe className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Multi-Language Support</CardTitle>
                <CardDescription>
                  Expand globally with automated translation and localization for 50+ languages and markets.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-muted/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto mb-8">
              Start free, scale as you grow. No hidden fees, no surprises. 
              Cancel anytime with our 30-day money-back guarantee.
            </p>
            
            <PricingFrequencyToggle frequency={frequency} setFrequency={setFrequency} />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan) => (
              <PricingCard key={plan.name} plan={plan} frequency={frequency} />
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <TestimonialsSection />

      {/* FAQ Section */}
      <section id="faq" className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-muted-foreground text-lg">
                Got questions? We've got answers. Can't find what you're looking for? 
                <a href="#contact" className="text-primary hover:underline ml-1">Contact our team</a>.
              </p>
            </div>
            
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1">
                <AccordionTrigger>How quickly can I see results?</AccordionTrigger>
                <AccordionContent>
                  Most users see initial improvements in search rankings within 2-4 weeks. 
                  Significant traffic increases typically occur within 2-3 months of consistent use. 
                  Results depend on your niche competitiveness and content quality.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-2">
                <AccordionTrigger>Do I need technical SEO knowledge?</AccordionTrigger>
                <AccordionContent>
                  Not at all! SEOFlow is designed for non-technical users. Our AI handles the complex SEO optimization automatically. 
                  You just need to provide your business information and content preferences.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-3">
                <AccordionTrigger>Can I cancel anytime?</AccordionTrigger>
                <AccordionContent>
                  Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees. 
                  You'll continue to have access until the end of your current billing period.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-4">
                <AccordionTrigger>What platforms do you integrate with?</AccordionTrigger>
                <AccordionContent>
                  We integrate with 50+ platforms including WordPress, Shopify, Webflow, Ghost, Medium, 
                  and all major CMS platforms. We also have API access for custom integrations.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-5">
                <AccordionTrigger>Is there a free trial?</AccordionTrigger>
                <AccordionContent>
                  Yes! We offer a 14-day free trial with full access to all features. 
                  No credit card required to start. You can upgrade to a paid plan anytime during or after the trial.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-6">
                <AccordionTrigger>How does the ROI tracking work?</AccordionTrigger>
                <AccordionContent>
                  Our ROI tracking connects your organic traffic to actual revenue through Google Analytics integration 
                  and conversion tracking. You'll see exactly which content pieces drive sales and their monetary value.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-muted/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Scale Your Content Marketing?
            </h2>
            <p className="text-muted-foreground text-lg mb-8">
              Join 10,000+ developers and marketers who've already transformed their SEO strategy
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="text-lg px-8">
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8">
                Schedule Demo
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="flex flex-col items-center gap-2">
                <Mail className="w-6 h-6 text-primary" />
                <span className="font-medium">Email</span>
                <span className="text-muted-foreground text-sm"><EMAIL></span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <Phone className="w-6 h-6 text-primary" />
                <span className="font-medium">Phone</span>
                <span className="text-muted-foreground text-sm">+****************</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <MapPin className="w-6 h-6 text-primary" />
                <span className="font-medium">Office</span>
                <span className="text-muted-foreground text-sm">San Francisco, CA</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Search className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold">SEOFlow</span>
              </div>
              <p className="text-muted-foreground text-sm">
                AI-powered SEO automation for developers and marketers who want results without the complexity.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><a href="#" className="hover:text-foreground">Features</a></li>
                <li><a href="#" className="hover:text-foreground">Pricing</a></li>
                <li><a href="#" className="hover:text-foreground">API</a></li>
                <li><a href="#" className="hover:text-foreground">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><a href="#" className="hover:text-foreground">Documentation</a></li>
                <li><a href="#" className="hover:text-foreground">Blog</a></li>
                <li><a href="#" className="hover:text-foreground">Case Studies</a></li>
                <li><a href="#" className="hover:text-foreground">Help Center</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><a href="#" className="hover:text-foreground">About</a></li>
                <li><a href="#" className="hover:text-foreground">Privacy</a></li>
                <li><a href="#" className="hover:text-foreground">Terms</a></li>
                <li><a href="#" className="hover:text-foreground">Contact</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>&copy; 2024 SEOFlow. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}